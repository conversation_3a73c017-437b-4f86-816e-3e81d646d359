import { NextRequest, NextResponse } from 'next/server';
import { validateQrCode } from '@/lib/database';

/**
 * GET /api/qr/validate/[code] - Validate a QR code and return associated archive
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ code: string }> }
) {
  try {
    const { code } = await params;
    
    if (!code) {
      return NextResponse.json(
        { error: 'QR code is required' },
        { status: 400 }
      );
    }

    // Validate the QR code
    const validation = await validateQrCode(code);
    
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          valid: false,
          message: 'Invalid QR code'
        },
        { status: 404 }
      );
    }

    // Return validation result with archive information
    return NextResponse.json(
      {
        valid: true,
        message: 'Valid QR code',
        archive: {
          id: validation.archive?.id,
          document_name: validation.archive?.document_name,
          applicant_name: validation.archive?.applicant_name,
          uploaded_at: validation.archive?.uploaded_at,
          approved_at: validation.archive?.approved_at,
          // Don't return document_data for security/performance reasons
        }
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error validating QR code:', error);
    return NextResponse.json(
      { error: 'Failed to validate QR code' },
      { status: 500 }
    );
  }
}
