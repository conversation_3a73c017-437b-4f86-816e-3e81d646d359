import { NextRequest, NextResponse } from 'next/server';
import { getQrValidationCodesByArchivedId, getArchiveById } from '@/lib/database';

/**
 * GET /api/archives/[id]/qr-codes - Get QR validation codes for a specific archive
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const archiveId = parseInt(id);
    
    if (isNaN(archiveId)) {
      return NextResponse.json(
        { error: 'Invalid archive ID' },
        { status: 400 }
      );
    }

    // Verify archive exists
    const archive = await getArchiveById(archiveId);
    if (!archive) {
      return NextResponse.json(
        { error: 'Archive not found' },
        { status: 404 }
      );
    }

    // Get QR codes for this archive
    const qrCodes = await getQrValidationCodesByArchivedId(archiveId);

    return NextResponse.json(
      {
        archiveId: archiveId,
        qrCodes: qrCodes,
        count: qrCodes.length
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error fetching QR codes for archive:', error);
    return NextResponse.json(
      { error: 'Failed to fetch QR codes' },
      { status: 500 }
    );
  }
}
