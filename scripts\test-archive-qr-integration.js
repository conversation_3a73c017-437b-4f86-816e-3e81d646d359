#!/usr/bin/env node

/**
 * Test script for archive QR code integration
 * This script tests that QR codes are automatically generated when documents are archived
 */

const {
  initializeDatabase,
  createUser,
  createDocument,
  createArchive,
  getQrValidationCodesByArchivedId,
  validateQrCode,
  deleteArchive,
  deleteQrValidationCodesByArchivedId,
  closeDatabase
} = require('../src/lib/database.ts');

async function testArchiveQrIntegration() {
  console.log('🧪 Testing Archive QR Code Integration...\n');

  try {
    // Initialize database
    console.log('📦 Initializing database...');
    await initializeDatabase();
    console.log('✅ Database initialized\n');

    // Create a test user
    console.log('👤 Creating test user...');
    const uniqueUsername = `archive_qr_test_${Date.now()}`;
    const userId = await createUser(uniqueUsername, 'password123', 'recovery_key');
    console.log(`✅ Test user created with ID: ${userId}\n`);

    // Create a test document
    console.log('📄 Creating test document...');
    const documentId = await createDocument(
      'Test Certificate for QR',
      '<PERSON>',
      Buffer.from('test document data for QR integration'),
      'approved',
      userId
    );
    console.log(`✅ Test document created with ID: ${documentId}\n`);

    // Test 1: Create an archive (simulating the archive API behavior)
    console.log('📁 Test 1: Creating archive...');
    const archiveId = await createArchive(
      'Test Certificate for QR',
      'Jane Smith',
      Buffer.from('test document data for QR integration'),
      userId,
      documentId,
      new Date().toISOString(),
      userId
    );
    console.log(`✅ Archive created with ID: ${archiveId}\n`);

    // Test 2: Manually create QR code (simulating what the API does)
    console.log('🔗 Test 2: Generating QR code for archive...');
    const qrCode = `LDIS_${archiveId}_${Date.now()}`;
    const { createQrValidationCode } = require('../src/lib/database.ts');
    const qrValidationId = await createQrValidationCode(qrCode, archiveId);
    console.log(`✅ QR code generated: ${qrCode} (ID: ${qrValidationId})\n`);

    // Test 3: Verify QR codes are associated with archive
    console.log('🔍 Test 3: Checking QR codes for archive...');
    const qrCodes = await getQrValidationCodesByArchivedId(archiveId);
    console.log(`✅ Found ${qrCodes.length} QR code(s) for archive ID ${archiveId}`);
    qrCodes.forEach((qr, index) => {
      console.log(`   ${index + 1}. Code: ${qr.code}, Created: ${qr.created_at}`);
    });
    console.log();

    // Test 4: Validate the generated QR code
    console.log('✅ Test 4: Validating generated QR code...');
    const validation = await validateQrCode(qrCode);
    console.log('✅ QR code validation result:', {
      isValid: validation.isValid,
      documentName: validation.archive?.document_name,
      applicantName: validation.archive?.applicant_name
    });
    console.log();

    // Test 5: Test QR code deletion when archive is deleted (simulating restore)
    console.log('🗑️ Test 5: Testing QR code cleanup on archive deletion...');
    console.log('   Deleting QR codes for archive...');
    await deleteQrValidationCodesByArchivedId(archiveId);
    
    console.log('   Verifying QR codes are deleted...');
    const remainingQrCodes = await getQrValidationCodesByArchivedId(archiveId);
    console.log(`✅ QR codes after deletion: ${remainingQrCodes.length} (should be 0)`);
    
    console.log('   Verifying QR code validation fails...');
    const invalidValidation = await validateQrCode(qrCode);
    console.log(`✅ QR code validation after deletion: ${invalidValidation.isValid} (should be false)\n`);

    // Test 6: Clean up - delete the archive
    console.log('🧹 Test 6: Cleaning up test data...');
    await deleteArchive(archiveId);
    console.log('✅ Test archive deleted\n');

    console.log('🎉 All Archive QR Code Integration tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Archives can have QR codes generated');
    console.log('   ✅ QR codes are properly linked to archives');
    console.log('   ✅ QR code validation works correctly');
    console.log('   ✅ QR codes are cleaned up when archives are deleted');
    console.log('   ✅ Integration ready for API implementation');

  } catch (error) {
    console.error('❌ Error during Archive QR Integration testing:', error);
    process.exit(1);
  } finally {
    // Close database connection
    await closeDatabase();
    console.log('\n🔒 Database connection closed');
  }
}

// Run the test
testArchiveQrIntegration();
