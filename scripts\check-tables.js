#!/usr/bin/env node

const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, '..', 'data', 'ldis.db');

console.log('📊 Checking database tables...\n');

const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Error opening database:', err.message);
    process.exit(1);
  }
  
  console.log('✅ Connected to database\n');
  
  // Get all tables
  db.all("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;", (err, rows) => {
    if (err) {
      console.error('❌ Error fetching tables:', err.message);
      db.close();
      process.exit(1);
    }
    
    console.log('📋 Database tables:');
    rows.forEach(row => {
      console.log(`   - ${row.name}`);
    });
    
    console.log('\n🔍 Checking QR validation codes table structure...');
    
    // Get QR validation codes table structure
    db.all("PRAGMA table_info(qr_validation_codes);", (err, columns) => {
      if (err) {
        console.error('❌ Error fetching table info:', err.message);
      } else if (columns.length === 0) {
        console.log('❌ QR validation codes table not found');
      } else {
        console.log('✅ QR validation codes table structure:');
        columns.forEach(col => {
          console.log(`   - ${col.name}: ${col.type}${col.notnull ? ' NOT NULL' : ''}${col.pk ? ' PRIMARY KEY' : ''}${col.dflt_value ? ` DEFAULT ${col.dflt_value}` : ''}`);
        });
        
        // Check if there are any QR codes in the table
        db.get("SELECT COUNT(*) as count FROM qr_validation_codes;", (err, result) => {
          if (err) {
            console.error('❌ Error counting QR codes:', err.message);
          } else {
            console.log(`\n📊 QR validation codes count: ${result.count}`);
          }
          
          db.close();
          console.log('\n🔒 Database connection closed');
        });
      }
    });
  });
});
