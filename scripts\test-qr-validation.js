#!/usr/bin/env node

/**
 * Test script for QR validation codes functionality
 * This script tests the new QR validation codes table and related functions
 */

const {
  initializeDatabase,
  createUser,
  createDocument,
  createArchive,
  createQrValidationCode,
  getQrValidationCodeById,
  getQrValidationCodeByCode,
  getQrValidationCodesByArchivedId,
  getAllQrValidationCodes,
  deleteQrValidationCode,
  validateQrCode,
  closeDatabase,
} = require("../src/lib/database.ts");

async function testQrValidationCodes() {
  console.log("🧪 Testing QR Validation Codes functionality...\n");

  try {
    // Initialize database
    console.log("📦 Initializing database...");
    await initializeDatabase();
    console.log("✅ Database initialized\n");

    // Create a test user with unique username
    console.log("👤 Creating test user...");
    const uniqueUsername = `qr_test_user_${Date.now()}`;
    const userId = await createUser(
      uniqueUsername,
      "password123",
      "recovery_key"
    );
    console.log(`✅ Test user created with ID: ${userId}\n`);

    // Create a test document
    console.log("📄 Creating test document...");
    const documentId = await createDocument(
      "Test Certificate",
      "John Doe",
      Buffer.from("test document data"),
      "approved",
      userId
    );
    console.log(`✅ Test document created with ID: ${documentId}\n`);

    // Create an archive
    console.log("📁 Creating test archive...");
    const archiveId = await createArchive(
      "Test Certificate",
      "John Doe",
      Buffer.from("test document data"),
      userId,
      documentId,
      new Date().toISOString(),
      userId
    );
    console.log(`✅ Test archive created with ID: ${archiveId}\n`);

    // Test 1: Create QR validation code
    console.log("🔗 Test 1: Creating QR validation code...");
    const qrCode = "QR_TEST_" + Date.now();
    const qrValidationId = await createQrValidationCode(qrCode, archiveId);
    console.log(
      `✅ QR validation code created with ID: ${qrValidationId}, Code: ${qrCode}\n`
    );

    // Test 2: Get QR validation code by ID
    console.log("🔍 Test 2: Getting QR validation code by ID...");
    const qrById = await getQrValidationCodeById(qrValidationId);
    console.log("✅ QR validation code retrieved by ID:", qrById);
    console.log(
      `   Code: ${qrById?.code}, Archive ID: ${qrById?.archived_id}\n`
    );

    // Test 3: Get QR validation code by code string
    console.log("🔍 Test 3: Getting QR validation code by code string...");
    const qrByCode = await getQrValidationCodeByCode(qrCode);
    console.log("✅ QR validation code retrieved by code:", qrByCode);
    console.log(
      `   ID: ${qrByCode?.id}, Archive ID: ${qrByCode?.archived_id}\n`
    );

    // Test 4: Get QR validation codes by archived ID
    console.log("🔍 Test 4: Getting QR validation codes by archived ID...");
    const qrsByArchiveId = await getQrValidationCodesByArchivedId(archiveId);
    console.log(
      `✅ Found ${qrsByArchiveId.length} QR validation codes for archive ID ${archiveId}`
    );
    qrsByArchiveId.forEach((qr, index) => {
      console.log(
        `   ${index + 1}. Code: ${qr.code}, Created: ${qr.created_at}`
      );
    });
    console.log();

    // Test 5: Get all QR validation codes
    console.log("🔍 Test 5: Getting all QR validation codes...");
    const allQrs = await getAllQrValidationCodes();
    console.log(
      `✅ Found ${allQrs.length} total QR validation codes in database\n`
    );

    // Test 6: Validate QR code
    console.log("✅ Test 6: Validating QR code...");
    const validation = await validateQrCode(qrCode);
    console.log("✅ QR code validation result:", validation);
    if (validation.isValid && validation.archive) {
      console.log(
        `   Valid QR code for document: ${validation.archive.document_name}`
      );
      console.log(`   Applicant: ${validation.archive.applicant_name}`);
    }
    console.log();

    // Test 7: Validate invalid QR code
    console.log("❌ Test 7: Validating invalid QR code...");
    const invalidValidation = await validateQrCode("INVALID_QR_CODE");
    console.log("✅ Invalid QR code validation result:", invalidValidation);
    console.log(`   Is valid: ${invalidValidation.isValid}\n`);

    // Test 8: Create another QR code for the same archive
    console.log(
      "🔗 Test 8: Creating second QR validation code for same archive..."
    );
    const qrCode2 = "QR_TEST_2_" + Date.now();
    const qrValidationId2 = await createQrValidationCode(qrCode2, archiveId);
    console.log(
      `✅ Second QR validation code created with ID: ${qrValidationId2}, Code: ${qrCode2}\n`
    );

    // Test 9: Get updated list of QR codes for archive
    console.log(
      "🔍 Test 9: Getting updated QR validation codes for archive..."
    );
    const updatedQrs = await getQrValidationCodesByArchivedId(archiveId);
    console.log(
      `✅ Found ${updatedQrs.length} QR validation codes for archive ID ${archiveId}`
    );
    updatedQrs.forEach((qr, index) => {
      console.log(
        `   ${index + 1}. Code: ${qr.code}, Created: ${qr.created_at}`
      );
    });
    console.log();

    // Test 10: Delete a QR validation code
    console.log("🗑️ Test 10: Deleting first QR validation code...");
    await deleteQrValidationCode(qrValidationId);
    console.log(`✅ QR validation code with ID ${qrValidationId} deleted\n`);

    // Test 11: Verify deletion
    console.log("🔍 Test 11: Verifying QR code deletion...");
    const deletedQr = await getQrValidationCodeById(qrValidationId);
    console.log(
      `✅ Deleted QR code lookup result: ${
        deletedQr ? "Found (ERROR!)" : "Not found (SUCCESS!)"
      }\n`
    );

    // Test 12: Final count
    console.log("🔍 Test 12: Final QR validation codes count...");
    const finalQrs = await getQrValidationCodesByArchivedId(archiveId);
    console.log(
      `✅ Final count: ${finalQrs.length} QR validation codes for archive ID ${archiveId}\n`
    );

    console.log("🎉 All QR validation codes tests completed successfully!");
  } catch (error) {
    console.error("❌ Error during QR validation codes testing:", error);
    process.exit(1);
  } finally {
    // Close database connection
    await closeDatabase();
    console.log("🔒 Database connection closed");
  }
}

// Run the test
testQrValidationCodes();
